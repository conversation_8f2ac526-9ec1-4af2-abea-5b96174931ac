# (1) 计算运算符
# print(5 / 2)
# print(5 % 2)
# print(5 % 3)
# # 判断奇数偶数
# x = 24
# print(x % 2 == 0)

# (2) 赋值运算符
# 自加
# a = 10
# # b = a+2
# # a = a + 2
# a += 2  # a = a + 2
# print(a)


# 案例1
# experience = 100
#
# print("打小妖怪,增加10个经验值")
# experience += 10
#
# print("打小妖怪,增加10个经验值")
# experience += 10
#
# print("打小妖怪,增加10个经验值")
# experience += 10
# print(experience)
# print("打大妖怪,增加50个经验值")
# experience += 50
#
# print("打大妖怪,增加50个经验值")
# experience += 50
#
# print(experience)

# 案例2
blood = 100

print("受到轻微攻击,掉血10")
blood -= 10  # blood = blood-10
print("当前血值:",blood)
