# 输入函数 input

# 案例1
# name = "yuan"
# age = 18
# name = input("请输入姓名：")
# print(name)
# age = input("请输入年龄：")
# print(age)
# print(f"姓名：{name} 年龄: {age} ")


# 案例2

# num1Str = input("num1:::")  # "100"   100
# num2Str = input("num2:::")  # "200"   200
# # print(type(num1Str))  # <class 'str'>
#
# # 数字字符串转数字  int(num1)
# num1Int = int(num1Str)  # 100
# num2Int = int(num2Str)  # 200
# # print(num1Int, type(num1Int))
#
# print(num1Int + num2Int)

# 输出函数 print

# print(100)
# print("200")
# print(True)
#
x = 1000
print(x)

print(100, "hello", x)
print(100, "hello", x, sep=",")
