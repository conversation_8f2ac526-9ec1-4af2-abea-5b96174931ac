# （1）比较表达式
# x = 10
# y = 3.14
#
# print(x == y)
# print(x > y)
# print(x <= y)


# （2）直接使用布尔值
# b1 = False
# print(b1)
# print(type(b1))  # <class 'bool'>

# （3）所有数据都有自己的bool值
# print(2 > 1)
# print(bool(2 > 1))
# print(bool(5 == 4))
# 零值：所有的数据类型中都有且只有一个值的bool状态是False,该值成为此类型的零值

# (1) 整型和浮点型的零值：0
# print(bool(1))
# print(bool(-1))
# print(bool(-100))
# print(bool(3.14))
# print(bool(-0.14))
# print(bool(0))

# (2) 字符串 ""  列表 [] 字典  {}
# print(bool(""))
# print(bool("0"))
# print(bool("-1"))
# print(bool("False"))

# print(bool([]))
# print(bool({}))

